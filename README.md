Accès aux questions ici

Instructions

Vous devez répondre à 2 questions parmi les 4 qui vous sont proposées. Ces questions sont conçues de manière à mettre en relation les notions abordées dans différents cours. Si vous répondez aux quatre questions, les deux meilleurs résultats seront comptabilisés.

Chaque réponse doit compter entre 500 et 750 mots
Vous disposez d'une semaine pour remettre le document
Remettez le travail en format word ou pdf 
Pour répondre à une question, vous devez

1) Mobiliser des connaissances acquises dans un minimum de trois séances (cours) par question.

Pour vous assurer de la clarté des références, assurez-vous d'identifier explicitement le cours que vous mobilisez dans votre réponse. 
2) Appuyer votre argument à l’aide d’exemples et d’écrits journalistiques (2 articles par question)

Vous disposez de toutes les ressources en ligne dont vous désirez.  Faites des recherches de manière à étayer votre argumentaire.
Vous devez référencer toutes les idées et citations que vous empruntez à des écrits et à des personnes. Puisqu’il ne s’agit pas d’une recherche écrite, vous pouvez simplement utiliser des hyperliens en guise de référencement.
Les citations ne doivent pas occuper plus de 15% de votre texte. Vous pouvez néanmoins paraphraser (et indiquer la référence) autant que vous le désirez.
3) Assurez-vous de bien présenter et définir les différents éléments de la question, puisque vous serez évalué sur votre compréhension de ceux-ci.

4) Voici les éléments de mise en page requis:

Identifiez chaque question (numéro et texte de la question) dans votre document avant d'y répondre. 
À la fin de la question, inscrivez le nombre de mots. 
Séparez votre texte de réponse en paragraphes (1 idée = 1 paragraphe)
Vous serez pénalisé si vous écrivez au delà de 800 mot par question, et en dessous de 400 mots par question.
5) Soignez votre orthographe et votre syntaxe. Vous ne serez pas pénalisé-es pour des coquilles, mais vous le serez si votre syntaxe et orthographe nuisent à la compréhension.

Aide moi à répondre avec cette question avec des articles ou des journals en français plus principalement pour Canada: 

Question 2: À partir d’une posture pragmatique et en mobilisant des leçons des crypto-wars et au moins un exemple contemporain, expliquez en quoi il n’est pas souhaitable pour des États d’introduire des portes dérobées (backdoor) sur des outils technologiques et de ne pas faire la divulgation de vulnérabilités zero-day.



Question 3: Expliquez à l'aide d'un exemple contemporain, comment la transparence des algorithmes peut être en tension avec le droit à la propriété intellectuelle dans le domaine de l'intelligence artificielle. À la lumière de cette réflexion, dans quelle mesure pensez-vous qu'il soit souhaitable de rendre obligatoire la divulgation du code source des algorithmes d'intelligence artificielle?
