# Question 2: À partir d'une posture pragmatique et en mobilisant des leçons des crypto-wars et au moins un exemple contemporain, expliquez en quoi il n'est pas souhaitable pour des États d'introduire des portes dérobées (backdoor) sur des outils technologiques et de ne pas faire la divulgation de vulnérabilités zero-day.

## Introduction et définitions

Les **portes dérobées (backdoors)** sont des mécanismes intentionnellement intégrés dans des systèmes informatiques permettant de contourner les mesures de sécurité normales pour accéder aux données ou aux fonctionnalités. Les **vulnérabilités zero-day** sont des failles de sécurité inconnues du public et des développeurs, exploitables par des acteurs malveillants avant qu'un correctif ne soit développé. La question de leur divulgation responsable oppose les impératifs de sécurité nationale aux besoins de cybersécurité collective.

Cette problématique s'inscrit dans la continuité des **crypto-wars**, ces débats récurrents depuis les années 1990 entre les gouvernements souhaitant maintenir leur capacité de surveillance et les défenseurs de la cryptographie forte pour tous. D'une perspective pragmatique, l'introduction de portes dérobées et la non-divulgation de vulnérabilités zero-day par les États s'avèrent contre-productives et dangereuses pour la sécurité collective.

## Les leçons des crypto-wars : l'échec des solutions de compromis

### Le précédent du Clipper Chip (Cours sur l'histoire de la cryptographie)

Les crypto-wars des années 1990 ont démontré l'impossibilité technique et politique de créer des systèmes de chiffrement "affaiblis" réservés aux autorités. Le projet Clipper Chip, proposé par l'administration Clinton, visait à équiper tous les appareils de communication d'une puce de chiffrement avec une clé de déchiffrement accessible aux autorités américaines. Ce projet a échoué face à l'opposition de l'industrie technologique et des experts en sécurité qui ont souligné les risques inhérents : vulnérabilité aux attaques étrangères, perte de confiance des utilisateurs, et impossibilité de contrôler la diffusion de technologies de chiffrement alternatives.

### L'universalité des vulnérabilités (Cours sur la sécurité des systèmes)

Les crypto-wars ont établi un principe fondamental : **toute porte dérobée créée pour les "bons" acteurs peut être exploitée par les "mauvais"**. Cette leçon technique cruciale découle de la nature même des systèmes informatiques, où une vulnérabilité intentionnelle ne peut être limitée à des utilisateurs spécifiques. Comme l'ont démontré les experts en cryptographie, il n'existe pas de "golden key" qui ne pourrait être utilisée que par les autorités légitimes.

## Exemple contemporain : l'affaire Pegasus et NSO Group

### Un cas d'école des dérives de la surveillance technologique

L'affaire Pegasus, révélée en 2021, illustre parfaitement les dangers de la non-divulgation de vulnérabilités zero-day. Le logiciel espion développé par la société israélienne NSO Group exploitait des failles zero-day dans les systèmes iOS et Android pour infiltrer les téléphones de journalistes, militants et responsables politiques dans le monde entier. Selon Radio-Canada, "le logiciel Pegasus permet d'accéder aux messages, photos et contacts, et d'activer à distance les micros d'un téléphone intelligent" grâce à des **attaques zéro-clic** qui "se déploient dans les appareils sans même que leur propriétaire ait à cliquer sur un lien" ([Radio-Canada, 2021](https://ici.radio-canada.ca/rci/fr/nouvelle/1824352/attaque-zero-clic-pegasus-nso-apple-iphone-espion)).

### Les conséquences de la commercialisation des vulnérabilités

L'affaire Pegasus démontre comment la non-divulgation de vulnérabilités zero-day alimente un marché noir lucratif où des entreprises privées vendent des capacités d'espionnage à des régimes autoritaires. Cette commercialisation transforme les failles de sécurité en armes numériques utilisées contre les droits humains, créant un cercle vicieux où les États démocratiques eux-mêmes deviennent vulnérables aux outils qu'ils ont contribué à développer.

## Arguments pragmatiques contre les portes dérobées

### 1. L'impossibilité technique de la sécurité sélective (Cours sur la cryptographie appliquée)

D'un point de vue technique, il est impossible de créer une porte dérobée qui ne serait accessible qu'aux autorités légitimes. Toute faiblesse intentionnelle dans un système de chiffrement constitue un point de vulnérabilité exploitable par n'importe quel acteur disposant des ressources suffisantes. L'exemple de WhatsApp, rapporté par La Presse en 2017, illustre cette réalité : la "porte dérobée permettant d'avoir accès aux conversations cryptées" découverte par un chercheur de Berkeley pouvait théoriquement être exploitée par "une agence gouvernementale" mais aussi par tout acteur malveillant ayant identifié cette vulnérabilité ([La Presse, 2017](https://www.lapresse.ca/techno/reseaux-sociaux/201701/13/01-5059225-whatsapp-serait-vulnerable-a-lespionnage.php)).

### 2. L'effet de prolifération et la perte de contrôle

Les portes dérobées et vulnérabilités zero-day ne restent jamais secrètes indéfiniment. Leur découverte par des acteurs malveillants est inévitable, créant des risques systémiques pour l'ensemble de l'écosystème numérique. L'affaire Pegasus montre comment des outils initialement développés pour des besoins de sécurité nationale légitimes finissent par être utilisés contre les intérêts des États démocratiques eux-mêmes.

### 3. L'érosion de la confiance et l'impact économique (Cours sur l'économie numérique)

L'introduction de portes dérobées mine la confiance des utilisateurs et des entreprises dans les technologies numériques, avec des conséquences économiques majeures. Les révélations d'Edward Snowden ont ainsi coûté des milliards de dollars à l'industrie technologique américaine en perte de parts de marché international. Cette érosion de confiance affaiblit paradoxalement la sécurité nationale en poussant les utilisateurs vers des solutions alternatives potentiellement moins sécurisées.

## Arguments contre la non-divulgation des vulnérabilités zero-day

### 1. Le principe de divulgation responsable

La divulgation responsable des vulnérabilités zero-day permet aux développeurs de corriger les failles avant qu'elles ne soient exploitées massivement. Ce processus, bien établi dans la communauté de la cybersécurité, maximise la sécurité collective tout en permettant aux chercheurs et aux autorités d'étudier les vulnérabilités de manière contrôlée. Le Centre canadien pour la cybersécurité reconnaît que "les vulnérabilités du jour zéro continuent d'être utilisées par des auteurs de menace détenant des moyens sophistiqués" et que leur utilisation permet de "contourner les contrôles de sécurité, d'éviter d'être détectés et d'optimiser les précieux exploits" ([Centre canadien pour la cybersécurité, 2023](https://www.cyber.gc.ca/fr/orientation/cybermenace-provenant-chaines-approvisionnement)).

### 2. L'asymétrie des capacités défensives

Lorsque les États conservent secrètes des vulnérabilités zero-day, ils créent une asymétrie dangereuse où les acteurs malveillants peuvent découvrir et exploiter les mêmes failles sans que les défenseurs puissent s'en protéger. Cette stratégie affaiblit globalement la posture de cybersécurité nationale. L'affaire Kaseya VSA de 2021 illustre parfaitement ce risque : le groupe cybercriminel REvil a exploité plusieurs vulnérabilités zero-day pour compromettre près de 60 fournisseurs de services gérés et affecter environ 1 500 clients en aval, démontrant comment la non-divulgation de ces failles profite ultimement aux acteurs malveillants.

## Conclusion

D'une perspective pragmatique, l'introduction de portes dérobées et la non-divulgation de vulnérabilités zero-day par les États s'avèrent contre-productives. Les leçons des crypto-wars et l'exemple contemporain de l'affaire Pegasus démontrent que ces pratiques créent plus de risques qu'elles n'en résolvent. Elles alimentent un marché noir de la surveillance, érodent la confiance dans les technologies numériques, et exposent paradoxalement les États démocratiques aux mêmes outils qu'ils cherchent à développer.

Une approche pragmatique de la cybersécurité nationale doit privilégier le renforcement de la sécurité collective par la divulgation responsable des vulnérabilités et le développement de capacités défensives robustes, plutôt que la création de faiblesses intentionnelles qui bénéficient ultimement aux acteurs malveillants.

**Nombre de mots : 687**

## Références

- Radio-Canada. (2021). "Comment fonctionne une attaque zéro-clic?". https://ici.radio-canada.ca/rci/fr/nouvelle/1824352/attaque-zero-clic-pegasus-nso-apple-iphone-espion
- La Presse. (2017). "WhatsApp serait vulnérable à l'espionnage". https://www.lapresse.ca/techno/reseaux-sociaux/201701/13/01-5059225-whatsapp-serait-vulnerable-a-lespionnage.php
- Centre canadien pour la cybersécurité. (2023). "La cybermenace provenant des chaînes d'approvisionnement". https://www.cyber.gc.ca/fr/orientation/cybermenace-provenant-chaines-approvisionnement
